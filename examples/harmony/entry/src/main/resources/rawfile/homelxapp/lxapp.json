{"lxAppId": "<PERSON><PERSON><PERSON><PERSON>", "lxAppName": "Home LxApp Demo", "version": "1.0.0", "pages": ["pages/home/<USER>", "pages/API/index.tsx", "pages/todo/index.vue"], "debug": true, "tabBar": {"color": "#999999", "selectedColor": "#1677ff", "backgroundColor": "transparent", "borderStyle": "#eeeeee", "position": "left", "dimension": 60, "list": [{"text": "Home", "pagePath": "pages/home/<USER>", "iconPath": "images/home.png", "selectedIconPath": "images/home_selected.png", "selected": true, "group": "start"}, {"text": "API", "pagePath": "pages/API/index.tsx", "iconPath": "images/api.png", "group": "end"}, {"text": "ToDo", "pagePath": "pages/todo/index.vue", "iconPath": "images/todo.png", "selectedIconPath": "images/todo_selected.png", "group": "end"}]}}